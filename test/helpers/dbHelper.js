/**
 * Database helper functions for tests
 * Uses MongoDB Atlas 8.0.8
 */

const mongoose = require('mongoose');

/**
 * Connect to the test database
 * @returns {Promise<mongoose>} Mongoose instance
 */
async function connectTestDB() {
  if (mongoose.connection.readyState === 0) {
    try {
      // Connect to MongoDB Atlas
      await mongoose.connect(global.__MONGO_URI__, {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });

      console.log('Connected to MongoDB Atlas 8.0.8');

      // Log connection info (without credentials)
      const connectionUri = mongoose.connection.client.s.url;
      const sanitizedUri = connectionUri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
      console.log(`Database connection: ${sanitizedUri}`);
    } catch (error) {
      console.error('Failed to connect to MongoDB Atlas:', error.message);
      throw error;
    }
  }
  return mongoose;
}

/**
 * Disconnect from the test database
 * @returns {Promise<void>}
 */
async function disconnectDB() {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.disconnect();
  }
}

/**
 * Clear specific collections
 * @param {...string} modelNames - Names of Mongoose models to clear
 * @returns {Promise<void>}
 */
async function clearCollections(...modelNames) {
  if (mongoose.connection.readyState !== 1) {
    throw new Error('Database connection not established. Call connectTestDB() first.');
  }

  const db = mongoose.connection.db;

  try {
    for (const modelName of modelNames) {
      try {
        // Try to get the model, if it doesn't exist, try to require it
        let model;
        try {
          model = mongoose.model(modelName);
        } catch (modelError) {
          // Try to require the model file
          try {
            require(`../../models/${modelName.toLowerCase()}`);
            model = mongoose.model(modelName);
          } catch (requireError) {
            console.warn(`Model ${modelName} not found, skipping collection clear`);
            continue;
          }
        }

        const collectionName = model.collection.name;

        console.log(`Clearing test data from collection: ${collectionName}`);
        // Only clear documents that have a test_ prefix or specific test marker
        // This is safer than clearing the entire collection in a shared database
        await db.collection(collectionName).deleteMany({
          $or: [
            { _testData: true },
            { name: { $regex: /^test_/ } },
            { email: { $regex: /^test_/ } }
          ]
        });
      } catch (modelError) {
        console.warn(`Error clearing collection for model ${modelName}:`, modelError.message);
        // Continue with other models
      }
    }
  } catch (error) {
    console.error('Error clearing collections:', error);
    throw error;
  }
}

/**
 * Clear all collections in the database
 * @returns {Promise<void>}
 */
async function clearDatabase() {
  if (mongoose.connection.readyState !== 1) {
    throw new Error('Database connection not established. Call connectTestDB() first.');
  }

  const db = mongoose.connection.db;
  const collections = await db.collections();

  try {
    for (const collection of collections) {
      const collectionName = collection.collectionName;

      // Skip system collections
      if (collectionName.startsWith('system.')) {
        continue;
      }

      // Only clear test data
      console.log(`Safely clearing test data from collection: ${collectionName}`);
      await collection.deleteMany({
        $or: [
          { _testData: true },
          { name: { $regex: /^test_/ } },
          { email: { $regex: /^test_/ } }
        ]
      });
    }
  } catch (error) {
    console.error('Error clearing database:', error);
    throw error;
  }
}

/**
 * Find a document by ID
 * @param {string} modelName - Name of the Mongoose model
 * @param {string} id - Document ID
 * @returns {Promise<Object>} Found document
 */
async function findById(modelName, id) {
  try {
    let model;
    try {
      model = mongoose.model(modelName);
    } catch (modelError) {
      // Try to require the model file
      try {
        require(`../../models/${modelName.toLowerCase()}`);
        model = mongoose.model(modelName);
      } catch (requireError) {
        throw new Error(`Model ${modelName} not found`);
      }
    }
    return await model.findById(id);
  } catch (error) {
    console.warn(`Error finding document by ID for model ${modelName}:`, error.message);
    return null;
  }
}

/**
 * Find documents by query
 * @param {string} modelName - Name of the Mongoose model
 * @param {Object} query - Query object
 * @returns {Promise<Array>} Found documents
 */
async function find(modelName, query = {}) {
  const model = mongoose.model(modelName);
  return await model.find(query);
}

/**
 * Count documents by query
 * @param {string} modelName - Name of the Mongoose model
 * @param {Object} query - Query object
 * @returns {Promise<number>} Document count
 */
async function count(modelName, query = {}) {
  const model = mongoose.model(modelName);
  return await model.countDocuments(query);
}

/**
 * Create a document
 * @param {string} modelName - Name of the Mongoose model
 * @param {Object} data - Document data
 * @param {boolean} [markAsTestData=true] - Whether to mark the document as test data
 * @returns {Promise<Object>} Created document
 */
async function create(modelName, data, markAsTestData = true) {
  const model = mongoose.model(modelName);

  // Add test markers
  if (markAsTestData) {
    // Add a test marker to make it easy to identify and clean up test data
    data = {
      ...data,
      _testData: true
    };

    // If the document has a name field, prefix it with test_ if it doesn't already have the prefix
    if (data.name && !data.name.startsWith('test_')) {
      data.name = `test_${data.name}`;
    }

    // If the document has an email field, prefix it with test_ if it doesn't already have the prefix
    if (data.email && !data.email.startsWith('test_')) {
      data.email = `test_${data.email}`;
    }
  }

  return await model.create(data);
}

/**
 * Create multiple documents
 * @param {string} modelName - Name of the Mongoose model
 * @param {Array} dataArray - Array of document data
 * @param {boolean} [markAsTestData=true] - Whether to mark the documents as test data
 * @returns {Promise<Array>} Created documents
 */
async function createMany(modelName, dataArray, markAsTestData = true) {
  const model = mongoose.model(modelName);

  // Add test markers to each document
  if (markAsTestData) {
    dataArray = dataArray.map(data => {
      // Add a test marker
      const markedData = { ...data, _testData: true };

      // If the document has a name field, prefix it with test_ if it doesn't already have the prefix
      if (markedData.name && !markedData.name.startsWith('test_')) {
        markedData.name = `test_${markedData.name}`;
      }

      // If the document has an email field, prefix it with test_ if it doesn't already have the prefix
      if (markedData.email && !markedData.email.startsWith('test_')) {
        markedData.email = `test_${markedData.email}`;
      }

      return markedData;
    });
  }

  return await model.insertMany(dataArray);
}

/**
 * Update a document
 * @param {string} modelName - Name of the Mongoose model
 * @param {string} id - Document ID
 * @param {Object} data - Update data
 * @returns {Promise<Object>} Updated document
 */
async function update(modelName, id, data) {
  const model = mongoose.model(modelName);
  return await model.findByIdAndUpdate(id, data, { new: true });
}

/**
 * Delete a document
 * @param {string} modelName - Name of the Mongoose model
 * @param {string} id - Document ID
 * @returns {Promise<Object>} Deleted document
 */
async function remove(modelName, id) {
  const model = mongoose.model(modelName);
  return await model.findByIdAndDelete(id);
}

module.exports = {
  connectTestDB,
  disconnectDB,
  clearCollections,
  clearDatabase,
  findById,
  find,
  count,
  create,
  createMany,
  update,
  remove
};
